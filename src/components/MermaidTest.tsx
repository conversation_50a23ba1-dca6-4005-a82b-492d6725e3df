import { useEffect, useRef } from "react";
import mermaid from "mermaid";

const MermaidTest = () => {
  const mermaidRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let isMounted = true;

    const initAndRender = async () => {
      if (!mermaidRef.current || !isMounted) return;

      const graphDefinition = `graph TD
    A[User] --> B[App]
    B --> C[Feature]`;

      try {
        // Initialize mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
        });

        console.log('Test: Rendering mermaid diagram');

        // Method 1: Using render
        const { svg } = await mermaid.render('test-graph-' + Date.now(), graphDefinition);

        if (isMounted && mermaidRef.current) {
          // Create wrapper to prevent React conflicts
          const wrapper = document.createElement('div');
          wrapper.innerHTML = svg;

          mermaidRef.current.innerHTML = '';
          mermaidRef.current.appendChild(wrapper);
          console.log('Mermaid test render successful');
        }
      } catch (error) {
        console.error('Mermaid test error:', error);
        if (isMounted && mermaidRef.current) {
          mermaidRef.current.innerHTML = `<div class="text-red-500">Error: ${error.message}</div>`;
        }
      }
    };

    // Add delay to ensure DOM is ready
    const timer = setTimeout(initAndRender, 200);
    return () => {
      isMounted = false;
      clearTimeout(timer);
    };
  }, []);

  return (
    <div className="p-4 border rounded">
      <h3 className="mb-4">Mermaid Test</h3>
      <div ref={mermaidRef} className="border p-4 min-h-[200px]">
        Loading...
      </div>
    </div>
  );
};

export default MermaidTest;
