import { useEffect, useRef } from "react";
import mermaid from "mermaid";

const MermaidTest = () => {
  const mermaidRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const initAndRender = async () => {
      if (!mermaidRef.current) return;

      const graphDefinition = `graph TD
    A[User] --> B[App]
    B --> C[Feature]`;

      try {
        // Initialize mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
        });

        console.log('Test: Rendering mermaid diagram');

        // Method 1: Using render
        const { svg } = await mermaid.render('test-graph-' + Date.now(), graphDefinition);
        mermaidRef.current.innerHTML = svg;
        console.log('Mermaid test render successful');
      } catch (error) {
        console.error('Mermaid test error:', error);
        mermaidRef.current.innerHTML = `<div class="text-red-500">Error: ${error.message}</div>`;
      }
    };

    // Add delay to ensure DOM is ready
    const timer = setTimeout(initAndRender, 200);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="p-4 border rounded">
      <h3 className="mb-4">Mermaid Test</h3>
      <div ref={mermaidRef} className="border p-4 min-h-[200px]">
        Loading...
      </div>
    </div>
  );
};

export default MermaidTest;
