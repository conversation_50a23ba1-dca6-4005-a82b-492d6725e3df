import { useState } from "react"
import { Search } from "lucide-react"
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"

interface IdeaSearchProps {
  ideas: Array<{ id: string; title: string; description: string }>
  onSelectIdea?: (ideaId: string) => void
}

export function IdeaSearch({ ideas, onSelectIdea }: IdeaSearchProps) {
  const [open, setOpen] = useState(false)

  return (
    <>
      <div 
        className="relative flex-1 max-w-md cursor-pointer"
        onClick={() => setOpen(true)}
      >
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <div className="h-10 w-full rounded-md border border-input bg-background pl-10 pr-3 py-2 text-sm text-muted-foreground">
          search...
        </div>
      </div>
      
      <CommandDialog open={open} onOpenChange={setO<PERSON>}>
        <CommandInput placeholder="Search ideas..." />
        <CommandList>
          <CommandEmpty>No ideas found.</CommandEmpty>
          <CommandGroup heading="Ideas">
            {ideas.map((idea) => (
              <CommandItem
                key={idea.id}
                onSelect={() => {
                  onSelectIdea?.(idea.id)
                  setOpen(false)
                }}
              >
                <div>
                  <div className="font-medium">{idea.title}</div>
                  <div className="text-sm text-muted-foreground">{idea.description}</div>
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  )
}