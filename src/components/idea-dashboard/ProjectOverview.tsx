import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";

const ProjectOverview = ({ idea }) => (
  <div className="space-y-6">
    <Card>
      <CardHeader>
        <CardTitle>Project Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <p>{idea.description}</p>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Problem & Solution</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold">Problem</h3>
            <p>{idea.problem}</p>
          </div>
          <div>
            <h3 className="font-semibold">Solution</h3>
            <p>{idea.solution}</p>
          </div>
        </div>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Target Audience</CardTitle>
      </CardHeader>
      <CardContent>
        <p>{idea.targetAudience}</p>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Tech Stack</CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="list-disc list-inside">
          {idea.techStack.map((tech, index) => (
            <li key={index}>{tech}</li>
          ))}
        </ul>
      </CardContent>
    </Card>
    <Card>
      <CardHeader>
        <CardTitle>Pricing Plans</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {idea.pricingPlans.map((plan, index) => (
            <div key={index} className="border p-4 rounded-lg">
              <h3 className="font-semibold">{plan.name}</h3>
              <p className="text-2xl font-bold">{plan.price}</p>
              <ul className="list-disc list-inside mt-2">
                {plan.features.map((feature, i) => (
                  <li key={i}>{feature}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
);

export default ProjectOverview;
