import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useEffect, useRef, useState } from "react";
import { Copy, Edit, ChevronDown, ChevronUp, Save, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import mermaid from "mermaid";

const Architecture = ({ idea }) => {
  const mermaidRef = useRef(null);
  const [mermaidCode, setMermaidCode] = useState(idea.mermaidDiagram);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editedCode, setEditedCode] = useState("");
  const [isCodeVisible, setIsCodeVisible] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (mermaidRef.current && mermaidCode) {
      mermaid.render("mermaid-graph", mermaidCode, (svgCode) => {
        mermaidRef.current.innerHTML = svgCode;
      });
    }
  }, [mermaidCode]);

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(mermaidCode);
      toast({
        title: "Copied!",
        description: "Mermaid diagram code copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy code to clipboard",
        variant: "destructive",
      });
    }
  };

  const handleEditClick = () => {
    setEditedCode(mermaidCode);
    setIsEditDialogOpen(true);
  };

  const handleSaveEdit = () => {
    setMermaidCode(editedCode);
    setIsEditDialogOpen(false);
    toast({
      title: "Updated!",
      description: "Architecture diagram has been updated",
    });
  };

  const handleCancelEdit = () => {
    setEditedCode("");
    setIsEditDialogOpen(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          App Architecture & User Flows
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyCode}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy Code
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleEditClick}
            >
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div ref={mermaidRef} id="mermaid-graph" className="border rounded-lg p-4 bg-muted/20"></div>

        <Collapsible open={isCodeVisible} onOpenChange={setIsCodeVisible}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="w-full justify-between">
              <span>View Mermaid Code</span>
              {isCodeVisible ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-2">
            <div className="bg-muted p-4 rounded-lg">
              <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                <code>{mermaidCode}</code>
              </pre>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle>Edit Architecture Diagram</DialogTitle>
          </DialogHeader>
          <div className="flex-1 space-y-4">
            <Textarea
              value={editedCode}
              onChange={(e) => setEditedCode(e.target.value)}
              placeholder="Enter your Mermaid diagram code here..."
              className="min-h-[300px] font-mono text-sm"
            />
            <div className="text-sm text-muted-foreground">
              <p>Use Mermaid syntax to create your diagram. Example:</p>
              <code className="bg-muted px-2 py-1 rounded text-xs">
                graph TD<br />
                &nbsp;&nbsp;A[User] --&gt; B[App]<br />
                &nbsp;&nbsp;B --&gt; C[Feature]
              </code>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelEdit}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={handleSaveEdit}>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default Architecture;
