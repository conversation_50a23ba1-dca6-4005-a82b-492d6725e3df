import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useEffect, useRef } from "react";
import mermaid from "mermaid";

const Architecture = ({ idea }) => {
  const mermaidRef = useRef(null);

  useEffect(() => {
    if (mermaidRef.current) {
      mermaid.render("mermaid-graph", idea.mermaidDiagram, (svgCode) => {
        mermaidRef.current.innerHTML = svgCode;
      });
    }
  }, [idea.mermaidDiagram]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>App Architecture & User Flows</CardTitle>
      </CardHeader>
      <CardContent>
        <div ref={mermaidRef} id="mermaid-graph"></div>
      </CardContent>
    </Card>
  );
};

export default Architecture;
