import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Copy } from "lucide-react";

const DevelopmentPlan = ({ idea }) => {
  const handleCopyPrompt = (prompt) => {
    navigator.clipboard.writeText(prompt);
    // Add a toast notification for feedback
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Development Plan</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {idea.developmentPlan.map((step, index) => (
          <div key={index}>
            <h3 className="font-semibold">{step.title}</h3>
            <p className="text-sm text-muted-foreground">{step.description}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleCopyPrompt(step.prompt)}
              className="mt-2"
            >
              <Copy className="mr-2 h-4 w-4" />
              Copy Prompt
            </Button>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default DevelopmentPlan;
