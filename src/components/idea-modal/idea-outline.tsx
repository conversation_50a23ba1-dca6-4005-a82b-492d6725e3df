import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { BarChart3, Code2, CreditCard } from "lucide-react";

interface IdeaOutlineProps {
  onEdit: () => void;
  onApprove: () => void;
}

export function IdeaOutline({ onEdit, onApprove }: IdeaOutlineProps) {
  const marketAnalysis = [
    { label: "Uniqueness", score: 8 },
    { label: "Stickiness", score: 9 },
    { label: "Growth Trend", score: 9 },
    { label: "Pricing Potential", score: 8 },
    { label: "Upsell Potential", score: 8 },
    { label: "Buying Power", score: 8 }
  ];

  const coreFeatures = [
    { name: "dashboard for posts", included: true },
    { name: "posting feature", included: true },
    { name: "posting feature", suggested: true },
    { name: "posting feature", suggested: true },
    { name: "posting feature", excluded: true }
  ];

  const techStack = [
    "Next.js", "React", "Tailwind CSS", "Shadcn/ui", "Clerk", "Supabase", "Framer Motion"
  ];

  const pricingTiers = [
    {
      name: "Starter",
      price: "$26/mo",
      description: "Perfect for solo...",
      features: ["basic usage", "limited features"]
    },
    {
      name: "Starter", 
      price: "$75/mo",
      description: "Perfect for teams...",
      features: ["moderate usage", "all features"]
    },
    {
      name: "Enterprise",
      price: "$99/mo",
      description: "Perfect for large companies...",
      features: ["unlimited usage", "all features", "unlimited ai", "unlimited ai"]
    }
  ];

  return (
    <div className="space-y-6 max-h-[80vh] overflow-y-auto">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-semibold mb-2">Twitter Clone</h2>
        <p className="text-muted-foreground text-sm">
          A minimalistic twitter clone that integrates multiple ai features and uses supabase for backend and auth.
        </p>
      </div>

      {/* Market Feasibility Analysis */}
      <div className="bg-muted/30 rounded-lg p-4 space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span className="font-medium">Market feasibility analysis</span>
          </div>
          <span className="text-sm font-medium">overall 8.2/10</span>
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          {marketAnalysis.map((item) => (
            <div key={item.label} className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{item.label}</span>
                <span>{item.score}/10</span>
              </div>
              <Progress value={item.score * 10} className="h-2" />
            </div>
          ))}
        </div>
      </div>

      {/* Core Features */}
      <div className="bg-muted/30 rounded-lg p-4 space-y-3">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 rounded-full bg-muted-foreground" />
          <span className="font-medium">Core Features</span>
        </div>
        
        <div className="space-y-2">
          {coreFeatures.map((feature, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <span className={`text-lg ${
                feature.included ? 'text-muted-foreground' : 
                feature.suggested ? 'text-green-500' : 'text-muted-foreground'
              }`}>
                {feature.included ? '—' : feature.suggested ? '+' : '—'}
              </span>
              <span className={feature.suggested ? 'text-green-500' : ''}>
                {feature.name}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Suggested Tech Stack */}
      <div className="bg-muted/30 rounded-lg p-4 space-y-3">
        <div className="flex items-center space-x-2">
          <Code2 className="h-4 w-4" />
          <span className="font-medium">Suggested Tech Stack</span>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {techStack.map((tech) => (
            <Badge key={tech} variant="secondary" className="text-xs">
              {tech}
            </Badge>
          ))}
        </div>
      </div>

      {/* Suggested Pricing */}
      <div className="bg-muted/30 rounded-lg p-4 space-y-4">
        <div className="flex items-center space-x-2">
          <CreditCard className="h-4 w-4" />
          <span className="font-medium">Suggested Pricing</span>
        </div>
        
        <div className="space-y-3">
          {pricingTiers.map((tier, index) => (
            <div key={index} className={`bg-muted/50 rounded-lg p-3 ${
              index === 2 ? 'col-span-2' : ''
            }`}>
              <div className="space-y-2">
                <div>
                  <h4 className="font-medium">{tier.name}</h4>
                  <div className="text-lg font-semibold">{tier.price}</div>
                  <p className="text-xs text-muted-foreground">{tier.description}</p>
                </div>
                <div className="space-y-1">
                  {tier.features.map((feature, fIndex) => (
                    <div key={fIndex} className="text-xs text-muted-foreground">
                      - {feature}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-3 pt-4">
        <Button variant="outline" onClick={onEdit} className="flex-1">
          Edit Details
        </Button>
        <Button onClick={onApprove} className="flex-1">
          Approve Plan
        </Button>
      </div>
    </div>
  );
}