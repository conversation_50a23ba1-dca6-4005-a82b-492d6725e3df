import { useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { BrainDumpForm } from "./brain-dump-form";
import { LoadingAnimation } from "./loading-animation";
import { IdeaOutline } from "./idea-outline";

interface IdeaModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type ModalState = "brain-dump" | "loading" | "outline";

export function IdeaModal({ open, onOpenChange }: IdeaModalProps) {
  const [state, setState] = useState<ModalState>("brain-dump");
  const [brainDump, setBrainDump] = useState("");

  const handleGenerate = (dump: string) => {
    setBrainDump(dump);
    setState("loading");
  };

  const handleLoadingComplete = () => {
    setState("outline");
  };

  const handleEdit = () => {
    setState("brain-dump");
  };

  const handleApprove = () => {
    onOpenChange(false);
    // Reset state for next time
    setTimeout(() => {
      setState("brain-dump");
      setBrainDump("");
    }, 300);
  };

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen);
    if (!newOpen) {
      // Reset state when modal closes
      setTimeout(() => {
        setState("brain-dump");
        setBrainDump("");
      }, 300);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
        {state === "brain-dump" && (
          <BrainDumpForm onGenerate={handleGenerate} />
        )}
        
        {state === "loading" && (
          <LoadingAnimation onComplete={handleLoadingComplete} />
        )}
        
        {state === "outline" && (
          <IdeaOutline onEdit={handleEdit} onApprove={handleApprove} />
        )}
      </DialogContent>
    </Dialog>
  );
}