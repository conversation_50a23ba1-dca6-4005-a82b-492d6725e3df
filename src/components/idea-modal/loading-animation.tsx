import { useEffect, useState } from "react";
import { Progress } from "@/components/ui/progress";

interface LoadingAnimationProps {
  onComplete: () => void;
}

const steps = [
  "Analyzing brain dump",
  "Generating project name",
  "Identifying core features",
  "Evaluating market feasability",
  "Determining Technical Requirements"
];

export function LoadingAnimation({ onComplete }: LoadingAnimationProps) {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  useEffect(() => {
    const duration = 5000; // 5 seconds total
    const interval = 50; // Update every 50ms
    const increment = 100 / (duration / interval);

    const timer = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + increment;
        
        // Update current step based on progress
        const stepIndex = Math.floor((newProgress / 100) * steps.length);
        setCurrentStep(Math.min(stepIndex, steps.length - 1));
        
        if (newProgress >= 100) {
          clearInterval(timer);
          setTimeout(onComplete, 500); // Small delay before showing results
          return 100;
        }
        return newProgress;
      });
    }, interval);

    return () => clearInterval(timer);
  }, [onComplete]);

  return (
    <div className="space-y-8">
      {/* Animated Background Elements */}
      <div className="relative h-32 overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="grid grid-cols-3 gap-4 opacity-20">
            {Array.from({ length: 12 }).map((_, i) => (
              <div
                key={i}
                className="h-4 bg-muted-foreground rounded animate-pulse"
                style={{
                  width: `${Math.random() * 60 + 40}px`,
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: `${1 + Math.random()}s`
                }}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Title */}
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-6">Generating Idea Outline</h2>
        
        {/* Progress Section */}
        <div className="space-y-4">
          <div className="flex justify-between items-center text-sm">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {/* Steps */}
      <div className="space-y-4">
        {steps.map((step, index) => (
          <div
            key={step}
            className={`flex items-center space-x-3 transition-all duration-300 ${
              index <= currentStep ? 'opacity-100' : 'opacity-30'
            }`}
          >
            <div className="flex-shrink-0">
              {index < currentStep ? (
                <div className="w-5 h-5 rounded-full bg-primary flex items-center justify-center">
                  <div className="w-2 h-2 bg-primary-foreground rounded-full" />
                </div>
              ) : index === currentStep ? (
                <div className="w-5 h-5 rounded-full border-2 border-primary animate-spin">
                  <div className="w-1 h-1 bg-primary rounded-full ml-0.5 mt-0.5" />
                </div>
              ) : (
                <div className="w-5 h-5 rounded-full border-2 border-muted-foreground/30" />
              )}
            </div>
            <span className={`text-sm ${index === currentStep ? 'font-medium' : ''}`}>
              {step}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}