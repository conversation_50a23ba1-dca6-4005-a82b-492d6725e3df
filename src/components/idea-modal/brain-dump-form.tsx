import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Mic } from "lucide-react";

interface BrainDumpFormProps {
  onGenerate: (brainDump: string) => void;
}

export function BrainDumpForm({ onGenerate }: BrainDumpFormProps) {
  const [brainDump, setBrainDump] = useState("");

  const handleSubmit = () => {
    if (brainDump.trim()) {
      onGenerate(brainDump);
    }
  };

  return (
    <div className="space-y-6">
      {/* Icon */}
      <div className="flex justify-center">
        <div className="w-16 h-12 bg-muted rounded-lg flex items-center justify-center">
          <div className="w-8 h-8 bg-muted-foreground/20 rounded"></div>
        </div>
      </div>

      {/* Title and Description */}
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Project Brain Dump</h2>
        <p className="text-muted-foreground">
          Turn you're brain dump into a concise app idea
        </p>
      </div>

      {/* Textarea */}
      <div className="relative">
        <Textarea
          placeholder="Write down everything you're thinking about, in any form!"
          value={brainDump}
          onChange={(e) => setBrainDump(e.target.value)}
          className="min-h-[200px] resize-none bg-muted/50 border-0 text-base"
        />
        <Button
          variant="ghost"
          size="icon"
          className="absolute bottom-3 right-3 h-8 w-8"
        >
          <Mic className="h-4 w-4" />
        </Button>
      </div>

      {/* Generate Button */}
      <Button 
        onClick={handleSubmit}
        className="w-full h-12 text-base"
        disabled={!brainDump.trim()}
      >
        Generate PRD and Suggestions
      </Button>
    </div>
  );
}