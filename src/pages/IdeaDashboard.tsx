import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import ProjectOverview from "@/components/idea-dashboard/ProjectOverview";
import Architecture from "@/components/idea-dashboard/Architecture";
import DevelopmentPlan from "@/components/idea-dashboard/DevelopmentPlan";
import MermaidTest from "@/components/MermaidTest";

const IdeaDashboard = () => {
  // Mock data - replace with actual data from your backend
  const idea = {
    name: "AI-Powered Personal Chef",
    description: "An app that acts as a personal chef, creating customized meal plans and assisting with cooking.",
    problem: "People are busy and find it hard to eat healthy, home-cooked meals.",
    solution: "An AI assistant that simplifies meal planning, grocery shopping, and cooking.",
    targetAudience: "Health-conscious professionals and busy families.",
    techStack: ["React Native", "Node.js", "PostgreSQL", "Spoonacular API"],
    pricingPlans: [
      { name: "Free", price: "$0/mo", features: ["Basic meal planning", "Limited recipes"] },
      { name: "<PERSON>", price: "$15/mo", features: ["Advanced meal planning", "Full recipe access", "Voice assistant"] },
    ],
    mermaidDiagram: `graph TD
    A[User] --> B[React Native App]
    B --> C[Authentication]
    B --> D[Meal Planning]
    B --> E[Recipe Assistant]
    D --> F[Node.js Backend]
    E --> F
    F --> G[(PostgreSQL Database)]
    F --> H[Spoonacular API]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style G fill:#e8f5e8`,
    developmentPlan: [
      { title: "Step 1: Set up the project", description: "Initialize a new React Native project and install dependencies.", prompt: "npx react-native init PersonalChefAI" },
      { title: "Step 2: Implement authentication", description: "Add user sign-up and login functionality.", prompt: "Implement user authentication using Firebase Auth." },
      { title: "Step 3: Build the meal planner", description: "Create the UI for generating and displaying meal plans.", prompt: "Build the meal planner UI with a weekly calendar view." },
    ],
  };

  return (
    <div className="container mx-auto p-4">
      <header className="mb-6">
        <h1 className="text-3xl font-bold">{idea.name}</h1>
      </header>

      <Tabs defaultValue="overview">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="architecture">Architecture</TabsTrigger>
          <TabsTrigger value="plan">Development Plan</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <ProjectOverview idea={idea} />
        </TabsContent>
        <TabsContent value="architecture">
          <div className="space-y-4">
            <MermaidTest />
            <Architecture idea={idea} />
          </div>
        </TabsContent>
        <TabsContent value="plan">
          <DevelopmentPlan idea={idea} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default IdeaDashboard;
