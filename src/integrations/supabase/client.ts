// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xjtnixkymnwobjdfdnmj.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhqdG5peGt5bW53b2JqZGZkbm1qIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyOTc3NzUsImV4cCI6MjA2ODg3Mzc3NX0.CEzMWn3zbjL2OxiQHBgpY-ZajcN_mV70gVCzyKl6H5k";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});